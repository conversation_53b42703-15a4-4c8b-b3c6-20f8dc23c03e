import axios from "axios";
import { logger } from "../config/logger";

/**
 * Service for managing expensive scraping operations efficiently
 * Prevents unnecessary calls during onboarding searches
 */
export class ScrapingService {
  private static instance: ScrapingService;
  private pendingScrapingRequests = new Map<string, Date>();
  private readonly SCRAPING_COOLDOWN_MINUTES = 30; // Don't scrape same retailer within 30 minutes
  private readonly SCRAPE_URL = "https://cannabis-marketing-chatbot-224bde0578da.herokuapp.com/api/v1/scrape-multiple-retailers?source=cannmenus";

  static getInstance(): ScrapingService {
    if (!ScrapingService.instance) {
      ScrapingService.instance = new ScrapingService();
    }
    return ScrapingService.instance;
  }

  /**
   * Check if a retailer was recently scraped to avoid duplicate expensive calls
   */
  private wasRecentlyScraped(retailerId: string): boolean {
    const lastScraped = this.pendingScrapingRequests.get(retailerId);
    if (!lastScraped) return false;

    const cooldownMs = this.SCRAPING_COOLDOWN_MINUTES * 60 * 1000;
    const timeSinceLastScrape = Date.now() - lastScraped.getTime();
    
    return timeSinceLastScrape < cooldownMs;
  }

  /**
   * Mark a retailer as recently scraped
   */
  private markAsScraped(retailerId: string): void {
    this.pendingScrapingRequests.set(retailerId, new Date());
  }

  /**
   * Trigger scraping for retailers with explicit user consent
   * This should only be called when user explicitly requests product data
   */
  async triggerScrapingWithConsent(
    retailerIds: string[],
    states: string[],
    userInitiated: boolean = false
  ): Promise<{ success: boolean; message: string; skippedCount: number }> {
    try {
      // Filter out recently scraped retailers to avoid duplicates
      const retailersToScrape = retailerIds.filter(id => !this.wasRecentlyScraped(id));
      const skippedCount = retailerIds.length - retailersToScrape.length;

      if (retailersToScrape.length === 0) {
        return {
          success: true,
          message: `All ${retailerIds.length} retailers were recently scraped. Skipping to avoid duplicate costs.`,
          skippedCount: retailerIds.length
        };
      }

      if (skippedCount > 0) {
        logger.info(
          `Skipping ${skippedCount} recently scraped retailers to avoid duplicate costs. ` +
          `Proceeding with ${retailersToScrape.length} retailers.`
        );
      }

      // Group retailers by state for efficient scraping
      const retailersByState = this.groupRetailersByState(retailersToScrape, states);

      let totalScraped = 0;
      for (const [state, stateRetailerIds] of Object.entries(retailersByState)) {
        await axios.post(this.SCRAPE_URL, {
          retailer_ids: stateRetailerIds,
          states: [state],
        });

        // Mark these retailers as scraped
        stateRetailerIds.forEach(id => this.markAsScraped(id));
        totalScraped += stateRetailerIds.length;

        logger.info(
          `Successfully triggered scraping for ${stateRetailerIds.length} retailers in ${state}` +
          (userInitiated ? " (user-initiated)" : "")
        );
      }

      return {
        success: true,
        message: `Successfully triggered scraping for ${totalScraped} retailers across ${Object.keys(retailersByState).length} states.`,
        skippedCount
      };
    } catch (error) {
      logger.error("Error triggering scraping operation:", error);
      return {
        success: false,
        message: `Failed to trigger scraping: ${error instanceof Error ? error.message : String(error)}`,
        skippedCount: 0
      };
    }
  }

  /**
   * Group retailer IDs by their states
   */
  private groupRetailersByState(retailerIds: string[], states: string[]): Record<string, string[]> {
    // For now, assume all retailers belong to the first state
    // In a more sophisticated implementation, you'd look up each retailer's state
    const primaryState = states[0];
    if (!primaryState) {
      logger.warn("No states provided for scraping operation");
      return {};
    }

    return {
      [primaryState]: retailerIds
    };
  }

  /**
   * Log retailers that need scraping without triggering expensive operations
   * This is used during search operations to identify potential scraping candidates
   */
  logRetailersNeedingScraping(
    retailerIds: string[],
    context: string = "search operation"
  ): void {
    if (retailerIds.length === 0) return;

    const newRetailers = retailerIds.filter(id => !this.wasRecentlyScraped(id));
    
    if (newRetailers.length > 0) {
      logger.info(
        `Found ${newRetailers.length} retailers with missing product data during ${context}. ` +
        `Retailer IDs: ${newRetailers.join(', ')}. ` +
        `Consider manual scraping if product data is needed.`
      );
    }

    if (newRetailers.length < retailerIds.length) {
      const recentlyScrapedCount = retailerIds.length - newRetailers.length;
      logger.info(
        `${recentlyScrapedCount} retailers were recently scraped and don't need immediate re-scraping.`
      );
    }
  }

  /**
   * Get statistics about scraping operations
   */
  getScrapingStats(): {
    totalTrackedRetailers: number;
    recentlyScrapedCount: number;
    oldestScrapeTime: Date | null;
    } {
    const now = new Date();
    const cooldownMs = this.SCRAPING_COOLDOWN_MINUTES * 60 * 1000;
    
    let recentlyScrapedCount = 0;
    let oldestScrapeTime: Date | null = null;

    for (const [_, scrapeTime] of this.pendingScrapingRequests) {
      const timeSinceLastScrape = now.getTime() - scrapeTime.getTime();
      
      if (timeSinceLastScrape < cooldownMs) {
        recentlyScrapedCount++;
      }

      if (!oldestScrapeTime || scrapeTime < oldestScrapeTime) {
        oldestScrapeTime = scrapeTime;
      }
    }

    return {
      totalTrackedRetailers: this.pendingScrapingRequests.size,
      recentlyScrapedCount,
      oldestScrapeTime
    };
  }

  /**
   * Clear old scraping records to prevent memory leaks
   */
  cleanupOldRecords(): void {
    const now = new Date();
    const maxAgeMs = 24 * 60 * 60 * 1000; // 24 hours
    
    let cleanedCount = 0;
    for (const [retailerId, scrapeTime] of this.pendingScrapingRequests) {
      const age = now.getTime() - scrapeTime.getTime();
      if (age > maxAgeMs) {
        this.pendingScrapingRequests.delete(retailerId);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      logger.info(`Cleaned up ${cleanedCount} old scraping records`);
    }
  }
}

// Export singleton instance
export const scrapingService = ScrapingService.getInstance();
