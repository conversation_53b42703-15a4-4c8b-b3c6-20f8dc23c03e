/**
 * @swagger
 * tags:
 *   name: Retailer
 *   description: Retailer management endpoints
 */

import Router from "@koa/router";
import { Context } from "koa";
import { logger } from "../config/logger";
import { locationRoleMiddleware } from "../locations/LocationService";
import { SupabaseService } from "../supabase/SupabaseService";
import App from "../app";
import { scrapingService } from "../services/ScrapingService";

const router = new Router();

const getSupabaseService = () => {
  return new SupabaseService({
    url: process.env.SUPABASE_URL || "",
    key: process.env.SUPABASE_SERVICE_ROLE_KEY || "",
    bucket: process.env.SUPABASE_BUCKET || "location-data",
  });
};

/**
 * @swagger
 * /retailers:
 *   get:
 *     summary: List Retailers
 *     description: Retrieves a paginated list of retailers for the current location
 *     tags: [Retailer]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of items per page
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *         description: Field to sort by
 *       - in: query
 *         name: direction
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *         description: Sort direction
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Retailer'
 *                 total:
 *                   type: integer
 *                 page:
 *                   type: integer
 *                 limit:
 *                   type: integer
 */
router.get("/", async (ctx) => {
  // ... existing code ...
});

/**
 * @swagger
 * /retailers:
 *   post:
 *     summary: Create Retailer
 *     description: Creates a new retailer for the current location
 *     tags: [Retailer]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/RetailerCreateParams'
 *     responses:
 *       200:
 *         description: Retailer created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Retailer'
 *       400:
 *         description: Invalid input
 */
router.post("/", locationRoleMiddleware("editor"), async (ctx) => {
  // ... existing code ...
});

/**
 * @swagger
 * /retailers/{retailerId}:
 *   get:
 *     summary: Get Retailer by ID
 *     description: Retrieves a specific retailer by its identifier
 *     tags: [Retailer]
 *     parameters:
 *       - in: path
 *         name: retailerId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Retailer identifier
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Retailer'
 *       404:
 *         description: Retailer not found
 */
router.get(
  "/:retailerId",
  locationRoleMiddleware("support"),
  async (ctx: Context) => {
    const { retailerId } = ctx.params;

    if (!retailerId) {
      ctx.status = 400;
      ctx.body = {
        error: "Retailer ID is required",
      };
      return;
    }

    try {
      const supabaseService = getSupabaseService();
      const retailer = await supabaseService.getRetailerById(retailerId);

      if (!retailer) {
        ctx.status = 404;
        ctx.body = {
          error: "Retailer not found",
        };
        return;
      }

      ctx.status = 200;
      ctx.body = { retailer };
    } catch (error) {
      logger.error("Error getting retailer:", error);
      ctx.status = 500;
      ctx.body = {
        error: "Failed to get retailer",
        details: error instanceof Error ? error.message : String(error),
      };
    }
  }
);

/**
 * @swagger
 * /retailers/{retailerId}:
 *   patch:
 *     summary: Update Retailer
 *     description: Updates an existing retailer
 *     tags: [Retailer]
 *     parameters:
 *       - in: path
 *         name: retailerId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Retailer identifier
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/RetailerUpdateParams'
 *     responses:
 *       200:
 *         description: Retailer updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Retailer'
 *       404:
 *         description: Retailer not found
 */
router.patch("/:retailerId", locationRoleMiddleware("editor"), async (ctx) => {
  // ... existing code ...
});

/**
 * @swagger
 * /retailers/{retailerId}:
 *   delete:
 *     summary: Delete Retailer
 *     description: Deletes a retailer
 *     tags: [Retailer]
 *     parameters:
 *       - in: path
 *         name: retailerId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Retailer identifier
 *     responses:
 *       204:
 *         description: Retailer deleted successfully
 *       404:
 *         description: Retailer not found
 */
router.delete("/:retailerId", locationRoleMiddleware("editor"), async (ctx) => {
  // ... existing code ...
});

// Search retailers in Supabase
router.get(
  "/search",
  locationRoleMiddleware("support"),
  async (ctx: Context) => {
    const { query } = ctx.query;

    if (!query || typeof query !== "string") {
      ctx.status = 400;
      ctx.body = {
        error: "Query parameter is required",
      };
      return;
    }

    try {
      const supabaseService = getSupabaseService();
      const retailers = await supabaseService.searchRetailers(query, 10);

      ctx.status = 200;
      ctx.body = { retailers };
    } catch (error) {
      logger.error("Error searching retailers:", error);
      ctx.status = 500;
      ctx.body = {
        error: "Failed to search retailers",
        details: error instanceof Error ? error.message : String(error),
      };
    }
  }
);

// Search nearby retailers in Supabase
router.get(
  "/nearby",
  locationRoleMiddleware("support"),
  async (ctx: Context) => {
    const {
      latitude,
      longitude,
      radius = "30",
      page = "1",
      pageSize = "4",
    } = ctx.query;

    if (!latitude || !longitude) {
      ctx.status = 400;
      ctx.body = {
        error: "Latitude and longitude parameters are required",
      };
      return;
    }

    try {
      const lat = parseFloat(latitude as string);
      const lng = parseFloat(longitude as string);
      const radiusMiles = parseFloat(radius as string);
      const currentPage = parseInt(page as string, 10);
      const limit = parseInt(pageSize as string, 10);

      if (
        isNaN(lat) ||
        isNaN(lng) ||
        isNaN(radiusMiles) ||
        isNaN(currentPage) ||
        isNaN(limit)
      ) {
        ctx.status = 400;
        ctx.body = {
          error: "Invalid parameters provided",
        };
        return;
      }

      const supabaseService = getSupabaseService();

      const retailers = await supabaseService.searchNearbyRetailers(
        lat,
        lng,
        50,
        limit * 2
      );

      const startIndex = (currentPage - 1) * limit;
      const paginatedRetailers = retailers.slice(
        startIndex,
        startIndex + limit
      );

      ctx.status = 200;
      ctx.body = {
        retailers: paginatedRetailers,
        total: retailers.length,
        page: currentPage,
        pageSize: limit,
        totalPages: Math.ceil(retailers.length / limit),
      };
    } catch (error) {
      logger.error("Error searching nearby retailers:", error);
      ctx.status = 500;
      ctx.body = {
        error: "Failed to search nearby retailers",
        details: error instanceof Error ? error.message : String(error),
      };
    }
  }
);

// Get retailer products
router.get(
  "/:retailerId/products",
  locationRoleMiddleware("support"),
  async (ctx: Context) => {
    const { retailerId } = ctx.params;
    const { limit = "100" } = ctx.query;

    if (!retailerId) {
      ctx.status = 400;
      ctx.body = {
        error: "Retailer ID is required",
      };
      return;
    }

    try {
      const limitNum = parseInt(limit as string);
      const supabaseService = getSupabaseService();
      const products = await supabaseService.getRetailerProducts(
        retailerId,
        limitNum
      );

      // Log if no products are found (but don't auto-trigger expensive scraping)
      if (products.total_count === 0) {
        // Use ScrapingService to log retailer needing scraping without triggering expensive calls
        scrapingService.logRetailersNeedingScraping(
          [retailerId],
          "retailer product lookup"
        );
      }

      ctx.status = 200;
      ctx.body = { products };
    } catch (error) {
      logger.error("Error getting retailer products:", error);
      ctx.status = 500;
      ctx.body = {
        error: "Failed to get retailer products",
        details: error instanceof Error ? error.message : String(error),
      };
    }
  }
);

export default router;
